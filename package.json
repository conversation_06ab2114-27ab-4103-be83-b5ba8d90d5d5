{"name": "mattheo-termine-portfolio", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9002", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit"}, "dependencies": {"@hookform/resolvers": "^4.1.3", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.6", "@swc/helpers": "^0.5.17", "@types/nodemailer": "^7.0.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.475.0", "next": "15.3.3", "nodemailer": "^7.0.5", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.2", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}