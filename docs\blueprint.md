# **App Name**: Portfolio Integrator

## Core Features:

- Multi-page Navigation: Implement a multi-page navigation (Home, Projects, About, Contact).
- Detailed Project Pages: Create individual project pages with detailed information (images, links, technologies used).
- About Section: Design and implement an 'About' section to showcase the developer's background and vision.
- Contact Form: Implement a contact form for direct messaging.

## Style Guidelines:

- Primary color: Deep Indigo (#3F51B5) to convey professionalism and stability.
- Background color: Light Grayish-Blue (#ECEFF1) for a clean and modern feel.
- Accent color: Teal (#009688) for highlighting important elements and calls to action.
- Body font: 'PT Sans', a humanist sans-serif, suitable for body text.
- Headline font: 'Space Grotesk', a proportional sans-serif with a computerized feel, for headers.
- Use a consistent set of minimalistic icons for navigation and to represent skills.
- Use a clean, grid-based layout with plenty of white space to ensure readability and a professional look.