import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { devisId, signature, clientInfo, signedAt } = await request.json();

    if (!devisId || !signature || !clientInfo || !signedAt) {
      return NextResponse.json(
        { error: 'Données manquantes' },
        { status: 400 }
      );
    }

    // En production, vous sauvegarderiez en base de données
    // Pour les tests, on simule la sauvegarde
    const signedDevis = {
      id: devisId,
      signature,
      clientInfo,
      signedAt,
      status: 'signed',
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
      userAgent: request.headers.get('user-agent') || 'unknown'
    };

    console.log('📝 Devis signé:', {
      devisId,
      client: clientInfo.name,
      signedAt,
      hasSignature: !!signature
    });

    // Template HTML pour l'email de notification au prestataire
    const htmlTemplateProvider = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Devis Signé - ${devisId}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 800px; margin: 0 auto; padding: 20px; }
          .header { background: #10b981; color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 20px; border: 1px solid #ddd; }
          .signature-box { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; text-align: center; }
          .signature-img { max-width: 300px; border: 1px solid #ddd; border-radius: 5px; }
          .client-info { background: white; padding: 15px; border-radius: 5px; margin: 10px 0; }
          .action-buttons { text-align: center; margin: 20px 0; }
          .btn { display: inline-block; padding: 12px 24px; margin: 0 10px; text-decoration: none; border-radius: 5px; font-weight: bold; }
          .btn-accept { background: #10b981; color: white; }
          .btn-reject { background: #ef4444; color: white; }
          .footer { background: #333; color: white; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Devis Signé !</h1>
            <p><strong>Devis ID:</strong> ${devisId}</p>
            <p><strong>Signé le:</strong> ${new Date(signedAt).toLocaleString('fr-FR')}</p>
          </div>
          
          <div class="content">
            <h3>Informations Client</h3>
            <div class="client-info">
              <p><strong>Nom:</strong> ${clientInfo.name}</p>
              ${clientInfo.company ? `<p><strong>Entreprise:</strong> ${clientInfo.company}</p>` : ''}
              <p><strong>Email:</strong> ${clientInfo.email}</p>
              ${clientInfo.phone ? `<p><strong>Téléphone:</strong> ${clientInfo.phone}</p>` : ''}
            </div>

            <h3>Signature Électronique</h3>
            <div class="signature-box">
              <p><strong>Signature capturée le:</strong> ${new Date(signedAt).toLocaleString('fr-FR')}</p>
              <img src="${signature}" alt="Signature du client" class="signature-img" />
            </div>

            <div class="action-buttons">
              <h3>Actions requises :</h3>
              <p>Vous devez maintenant accepter ou refuser ce projet signé.</p>
              <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:9004'}/admin/devis/${devisId}/accept" class="btn btn-accept">
                ✅ Accepter le Projet
              </a>
              <a href="${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:9004'}/admin/devis/${devisId}/reject" class="btn btn-reject">
                ❌ Refuser le Projet
              </a>
            </div>

            <div style="background: #fef3c7; padding: 15px; border-radius: 5px; margin: 15px 0;">
              <p><strong>⏰ Important :</strong> Répondez rapidement au client pour maintenir une bonne relation commerciale.</p>
            </div>
          </div>

          <div class="footer">
            <p>Signature électronique valide et horodatée</p>
            <p>IP: ${signedDevis.ipAddress} | ${new Date(signedAt).toISOString()}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // Template HTML pour l'email de confirmation au client
    const htmlTemplateClient = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Devis Signé - Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3F51B5; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 20px; border: 1px solid #ddd; }
          .footer { background: #333; color: white; padding: 15px; text-align: center; border-radius: 0 0 8px 8px; }
          .highlight { background: #10b981; color: white; padding: 15px; border-radius: 5px; text-align: center; margin: 15px 0; }
          .timeline { background: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Devis Signé avec Succès</h1>
            <p>Merci ${clientInfo.name} !</p>
          </div>
          
          <div class="content">
            <p>Bonjour ${clientInfo.name},</p>
            
            <p>Votre signature électronique a été enregistrée avec succès.</p>
            
            <div class="highlight">
              <h3>Devis #${devisId} - SIGNÉ</h3>
              <p>Signé le ${new Date(signedAt).toLocaleString('fr-FR')}</p>
            </div>
            
            <div class="timeline">
              <h3>Prochaines étapes :</h3>
              <ol>
                <li><strong>Validation (24-48h)</strong> - Matthéo examine votre devis signé</li>
                <li><strong>Confirmation</strong> - Vous recevez l'acceptation ou les ajustements</li>
                <li><strong>Acompte</strong> - Facturation de 30% pour démarrer</li>
                <li><strong>Développement</strong> - Création de votre site web</li>
                <li><strong>Livraison</strong> - Mise en ligne et formation</li>
              </ol>
            </div>
            
            <p><strong>Important :</strong> Votre signature est juridiquement valide et engage les deux parties selon les conditions du devis.</p>
            
            <p>Si vous avez des questions, n'hésitez pas à me contacter directement.</p>
            
            <p>Cordialement,<br>
            Matthéo Termine<br>
            Intégrateur Web Freelance</p>
          </div>

          <div class="footer">
            <p><EMAIL> | www.mattheo-termine.fr</p>
            <p>Signature horodatée: ${new Date(signedAt).toISOString()}</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // En production, vous enverriez les emails ici
    // Pour les tests, on simule
    console.log('📧 Email simulé envoyé au prestataire: Devis signé par', clientInfo.name);
    console.log('📧 Email simulé envoyé au client: Confirmation de signature');

    // Simulation d'attente
    await new Promise(resolve => setTimeout(resolve, 1000));

    return NextResponse.json({ 
      success: true, 
      message: 'Devis signé avec succès',
      devisId,
      signedAt
    });

  } catch (error) {
    console.error('Erreur lors de la signature du devis:', error);
    return NextResponse.json(
      { error: 'Erreur lors de la signature du devis' },
      { status: 500 }
    );
  }
}
